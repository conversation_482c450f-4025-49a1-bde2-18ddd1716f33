# FlatMate Codebase Onboarding Guide

## Overview

FlatMate is a PySide6-based personal finance management application built with a modular architecture. The application provides features for importing, categorizing, and analyzing financial transactions from various bank statements.

## Architecture Overview

### Core Technologies
- **Framework**: PySide6 (Qt for Python)
- **Database**: SQLite with custom ORM layer
- **Configuration**: JSON-based configuration system
- **Logging**: Custom singleton logger
- **Architecture**: MVP (Model-View-Presenter) pattern with eager-loaded modules

### Project Structure

```
flatmate/
├── src/fm/                          # Main source directory
│   ├── main.py                     # Application entry point
│   ├── module_coordinator.py       # Module lifecycle management
│   ├── core/                       # Core infrastructure
│   │   ├── config/                 # Configuration management
│   │   ├── data_services/          # Data access layer
│   │   ├── database/               # Database abstraction
│   │   ├── services/               # Cross-cutting concerns
│   │   └── utils/                  # Utility functions
│   ├── modules/                    # Feature modules
│   │   ├── base/                   # Base classes for modules
│   │   ├── home/                   # Home/dashboard module
│   │   ├── update_data/            # Data import/update module
│   │   ├── categorize/             # Transaction categorization module
│   │   └── [other modules...]
│   └── gui/                        # GUI components
│       ├── main_window.py          # Main application window
│       ├── styles/                 # Application styling
│       └── config/                 # GUI configuration
├── DOCS/                           # Documentation
├── tests/                          # Test suite
└── resources/                      # Static resources
```

## Key Architectural Patterns

### 1. Module System
- **Eager Loading**: All modules are instantiated at startup
- **Lifecycle Management**: Modules implement `setup()`, `show()`, `hide()` methods
- **Transition System**: Central coordinator manages module switching
- **Presenter Pattern**: Each module follows MVP with Presenter classes

### 2. Configuration System
- **Centralized**: Single `config` object manages all settings
- **Hierarchical**: Global, user, and runtime configurations
- **Type-safe**: Configuration keys defined as enums
- **Persistent**: Changes automatically saved to JSON files

### 3. Data Layer
- **Repository Pattern**: Abstract data access with concrete implementations
- **Service Layer**: Business logic separated from data access
- **Caching**: In-memory caching for performance
- **Migration**: Automatic database schema updates

### 4. Logging System
- **Singleton Pattern**: Single `log` object throughout application
- **Self-initializing**: Creates log files automatically
- **Level-based**: Standard logging levels (DEBUG, INFO, WARN, ERROR)
- **Session-based**: Each run creates new log file

## Development Setup

### Prerequisites
- Python 3.11+
- Virtual environment (`.venv_fm313` provided)
- PySide6 and dependencies (see requirements.txt)

### Running the Application
```bash
# From project root
cd flatmate
# Use virtual environment python directly
flatmate/.venv_fm313/Scripts/python.exe src/fm/main.py
```

### Running Tests
```bash
# From any directory, use full path to venv python
flatmate/.venv_fm313/Scripts/python.exe tests/test_real_csvs.py
```

## Module Development

### Creating a New Module

1. **Directory Structure**:
   ```
   modules/your_module/
   ├── __init__.py
   ├── your_presenter.py    # Main presenter class
   ├── your_view.py         # UI components
   └── your_model.py        # Data models (if needed)
   ```

2. **Required Methods**:
   - `setup()`: One-time initialization
   - `show(**params)`: Display the module
   - `hide()`: Hide the module

3. **Integration**:
   - Add to `ModuleCoordinator.initialize_modules()`
   - Update navigation mappings if needed

### Base Classes

All modules should inherit from appropriate base classes:
- **BasePresenter**: Core module functionality
- **BaseView**: UI component base
- **BaseModel**: Data model base (if applicable)

## Configuration Keys

Configuration is accessed through typed keys:
```python
from fm.core.config.keys import ConfigKeys

# Example usage
debug_mode = config.get_value(ConfigKeys.App.DEBUG_MODE, False)
```

## Common Development Tasks

### Adding a New Service
1. Create service class in `core/services/`
2. Follow singleton pattern if needed
3. Add initialization in `initialize_application()`

### Adding Database Tables
1. Define model in appropriate module
2. Add migration in `database/migrations/`
3. Update repository interfaces

### Adding GUI Components
1. Create view classes following Qt conventions
2. Use existing styling system
3. Follow established patterns for signals/slots

## Debugging

### Log Files
- Location: `logs/` directory (auto-created)
- Naming: `flatmate_YYYYMMDD_HHMMSS.log`
- Access: Use `from fm.core.services.logger import log`

### Debug Mode
- Enable via configuration: `ConfigKeys.App.DEBUG_MODE`
- Provides additional logging and UI feedback

### Common Issues
1. **Import errors**: Check virtual environment activation
2. **Database issues**: Delete `.db` files to reset
3. **GUI issues**: Check Qt platform plugins

## Testing

### Test Structure
- **Unit tests**: Individual component testing
- **Integration tests**: End-to-end workflow testing
- **Real data tests**: Testing with actual bank statements

### Test Commands
```bash
# Run specific test
python tests/test_specific_feature.py

# Run with coverage
python -m pytest tests/ --cov=src/fm
```

## Code Style

### Conventions
- **PEP 8** compliance
- **Type hints** throughout
- **Docstrings** for all public methods
- **Single responsibility** principle

### Naming
- **Classes**: PascalCase
- **Functions/Variables**: snake_case
- **Constants**: UPPER_SNAKE_CASE
- **Private**: _leading_underscore

## Architecture Health Metrics

- **Modularity**: High - modules are loosely coupled
- **Testability**: Good - dependency injection ready
- **Maintainability**: Improving with refactoring efforts
- **Performance**: Adequate for current feature set
- **Scalability**: Architecture supports growth

## Next Steps for New Developers

1. **Run the application** to understand user experience
2. **Review module structure** starting with `home/` module
3. **Examine data flow** from import to categorization
4. **Study configuration system** for customization
5. **Read existing tests** to understand expected behavior
6. **Set up development environment** with proper IDE configuration

## Resources

- **Documentation**: `DOCS/` directory contains feature specifications
- **Examples**: Existing modules provide implementation patterns
- **Tests**: Comprehensive test suite demonstrates usage
- **Configuration**: `fm.core.config` for all settings
- **Logging**: Use `log` object for all debugging output
