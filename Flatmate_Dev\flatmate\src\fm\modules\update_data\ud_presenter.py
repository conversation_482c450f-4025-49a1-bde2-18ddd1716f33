#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.
"""

import os
from pathlib import Path

import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import log 

# Import the data service for database operations
#from ...core.data_services.helpers import CSVToTransactionConverter # ! what is this for!? AI slop

from ...gui.services.info_bar_service import InfoBarService
from ...gui._shared_components.utils.dialog_utils import configure_auto_import
from ..base.base_presenter import BasePresenter
from ._view.ud_view import UpdateDataView
from .config.ud_config import ud_config
from .config.ud_keys import UpdateDataKeys
from .utils.option_types import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents
from .utils.dw_director import dw_director
from .view_context_manager import UpdateDataViewManager
from .simple_state_coordinator import SimpleStateCoordinator


class UpdateDataPresenter(BasePresenter):
    """Presenter for the Update Data module."""


    def __init__(self, main_window, gui_config=None, gui_keys=None):
        """Initialize the Update Data presenter.

        Args:
            main_window: The main window instance
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Call parent constructor
        super().__init__(main_window, gui_config, gui_keys)

        # Get the InfoBarService instance
        self.info_bar_service = InfoBarService.get_instance()

        # State tracking
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self._updating_source_option = False  # Flag to prevent signal loops

        # View manager for morphic UI
        self.view_manager = UpdateDataViewManager()

        # SimpleStateCoordinator will be initialized in _connect_signals after view creation
        self.state_coordinator = None

    def _create_view(self):
        """Create the view instance. Called once during setup."""
        return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)

    def _connect_signals(self):
        """Connect view signals to handlers. Called once during setup."""
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))

        # Connect to high-level business signals from ViewModel
        view_model = self.view.left_buttons.view_model
        view_model.folder_selection_requested.connect(self._handle_folder_selection)
        view_model.files_selection_requested.connect(self._handle_files_selection)
        view_model.archive_folder_selection_requested.connect(self._handle_archive_folder_selection)
        view_model.process_files_requested.connect(self._handle_process)
        view_model.update_database_toggled.connect(self._handle_update_database_change)

        # Keep backward compatibility for now
        self.view.source_select_requested.connect(self._handle_source_select)
        self.view.save_select_requested.connect(self._handle_save_select)
        self.view.source_option_changed.connect(self._handle_source_option_change)
        self.view.save_option_changed.connect(self._handle_save_option_change)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        # Initialize SimpleStateCoordinator now that view is available
        self.state_coordinator = SimpleStateCoordinator(self.view, self.view.guide_pane)

        log.debug("Signals connected")

        # Subscribe to Update Data events
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self._on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self._on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

    def _refresh_content(self, **params):
        """Refresh update data content when shown.

        This method is called every time the module becomes visible.
        It handles view state setup and configuration.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing UpdateData content")

        # Set the source option from config to remember user's last choice
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)

        # Configure view based on database mode using view manager
        is_database_mode = self.view.left_buttons.get_update_database()
        auto_import_status = self.view_manager.get_auto_import_status()

        self.view_manager.configure_view_for_workflow(
            self.view,
            is_database_mode=is_database_mode,
            auto_import_status=auto_import_status
        )

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()

        # Show the InfoBar with appropriate message based on mode
        self.info_bar_service.show()
        if is_database_mode and auto_import_status.get('enabled', False):
            pending_count = len(auto_import_status.get('pending_files', []))
            if pending_count > 0:
                self.info_bar_service.publish_message(
                    f"Auto-import found {pending_count} pending files ready for processing.", "INFO"
                )
            else:
                self.info_bar_service.publish_message(
                    "Auto-import is active. Drop files in the monitored folder or select manually.", "INFO"
                )
        else:
            self.info_bar_service.publish_message(
                "Select source files or folder to begin.", "INFO"
            )

        self._setup_view_from_config()

        log.debug("UpdateData content refresh complete")

    # New business signal handlers based on YOUR specification

    def _handle_folder_selection(self):
        """Handle folder selection request from ViewModel."""
        log.debug("Folder selection requested via business signal")
        self._handle_source_select(SourceOptions.SELECT_FOLDER.value)

    def _handle_files_selection(self):
        """Handle files selection request from ViewModel."""
        log.debug("Files selection requested via business signal")
        self._handle_source_select(SourceOptions.SELECT_FILES.value)

    def _handle_archive_folder_selection(self):
        """Handle archive folder selection request from ViewModel."""
        log.debug("Archive folder selection requested via business signal")
        self._handle_save_select()

    def _handle_source_select(self, selection_type: str):
        """Handle source selection request."""
        log.debug(f"Source selection requested for type: {selection_type}")
        initial_dir = str(
            ud_config.get_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
        )

        if selection_type == SourceOptions.SELECT_FOLDER.value:
            folder = self.view.show_folder_dialog(
                "Select Source Folder", initial_dir=initial_dir
            )
            if not folder:
                return

            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, folder)

            # Get all supported files from the folder
            extensions = ud_config.get_allowed_file_extensions()
            file_paths = [
                str(p)
                for p in Path(folder).iterdir()
                if p.is_file() and p.suffix.lower() in extensions
            ]

            if not file_paths:
                ext_list = ", ".join(extensions)
                self.view.show_error(f"No supported files found in selected folder. Looking for: {ext_list}")
                return

            # Create a dictionary with source information
            self.selected_source = {
                "type": "folder",
                "path": folder,
                "file_paths": file_paths,
            }

        elif selection_type == SourceOptions.SELECT_FILES.value:
            files = self.view.show_files_dialog(
                "Select Source Files", initial_dir=initial_dir
            )
            if not files:
                return

            # Save the directory of the first file for next time
            last_dir = os.path.dirname(files[0])
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, last_dir)

            # Create a dictionary with source information
            self.selected_source = {"type": "files", "file_paths": files}

        elif selection_type == SourceOptions.SET_AUTO_IMPORT.value:
            # Show the auto-import configuration dialog
            log.debug("Opening auto-import configuration dialog")
            success = configure_auto_import(self.view)

            if success:
                self.info_bar_service.publish_message(
                    "Auto-import configuration updated. Restart application for changes to take effect.",
                    "INFO"
                )
            else:
                self.info_bar_service.publish_message(
                    "Auto-import configuration cancelled.",
                    "INFO"
                )

            # Reset the combo box to the previous selection since this is a configuration action
            # Use flag to prevent signal loops
            self._updating_source_option = True
            try:
                last_source_option = ud_config.get_value(
                    UpdateDataKeys.Source.LAST_SOURCE_OPTION,
                    default=SourceOptions.SELECT_FOLDER.value
                )
                self.view.set_source_option(last_source_option)
            finally:
                self._updating_source_option = False
            return

        elif selection_type == SourceOptions.AUTO_IMPORT_FOLDER.value:
            # Check if button text is "Configure..." (auto-import already configured)
            if self.view.left_buttons.source_select_btn.text() == "Configure...":
                # Open auto-import configuration dialog
                from ...gui._shared_components.utils.dialog_utils import configure_auto_import

                result = configure_auto_import(self.view)
                if result:
                    self.info_bar_service.publish_message(
                        f"Auto-import reconfigured: {result.get('import_path', 'Updated')}", "SUCCESS"
                    )
                    # Refresh the UI to reflect changes
                    self._refresh_content()
                return

            # Handle auto-import folder selection - get pending files
            auto_import_status = self.view_manager.get_auto_import_status()

            if not auto_import_status.get('enabled', False):
                self.view.show_error("Auto-import is not configured. Please set up auto-import first.")
                return

            import_path = auto_import_status.get('path', '')
            pending_files = auto_import_status.get('pending_files', [])

            if not pending_files:
                self.info_bar_service.publish_message(
                    f"No pending files in auto-import folder: {import_path}", "INFO"
                )
                return

            # Create source selection from pending files
            self.selected_source = {
                "type": "auto_import",
                "path": import_path,
                "file_paths": pending_files,
            }

            self.info_bar_service.publish_message(
                f"Auto-import: {len(pending_files)} files ready for processing", "SUCCESS"
            )

        else:
            self.view.show_error(f"Unknown source selection type: {selection_type}")
            return

        # Update the view with the selected source
        if self.selected_source:
            self.view.display_selected_source(self.selected_source)
            self.view.set_process_mode()

            # Notify SimpleStateCoordinator about source selection
            if self.state_coordinator:
                if self.selected_source["type"] == "folder":
                    self.state_coordinator.set_source_folder(
                        self.selected_source["path"],
                        len(self.selected_source["file_paths"])
                    )
                else:  # files or auto_import
                    self.state_coordinator.set_source_files(
                        self.selected_source["file_paths"]
                    )

            # Notify ViewModel that source is configured (keep for backward compatibility)
            source_path = self.selected_source.get('path', '')
            if source_path:
                self.view.left_buttons.view_model.set_source_configured(source_path)

            # If save option is "Same as source", update the save location now
            if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                self._handle_save_option_change(SaveOptions.SAME_AS_SOURCE.value)

    def _handle_save_select(self):
        """Handle save location selection."""
        folder = self.view.show_folder_dialog(
            "Select Save Location",
            initial_dir=str(ud_config.get_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, default=str(Path.home()))),
        )
        if folder:
            self.save_location = folder
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, folder)
            self.view.set_save_path(folder)

            # Notify SimpleStateCoordinator about custom destination
            if self.state_coordinator:
                self.state_coordinator.set_destination_custom(folder)

            # Notify ViewModel that archive is configured (keep for backward compatibility)
            self.view.left_buttons.view_model.set_archive_configured(folder)
            self.info_bar_service.publish_message(f"Save location: {folder}")

    def _handle_source_option_change(self, option: str):
        """Handle source option change."""
        # Skip processing if we're programmatically updating the option
        if self._updating_source_option:
            return

        # Reset selected source when option changes
        self.selected_source = None
        self.view.display_selected_source(None)
        self.info_bar_service.clear()
        self.info_bar_service.publish_message(f"Selected source option: {option}")

        # Save the selected option to config to remember the choice (but not for auto-import)
        if option != SourceOptions.SET_AUTO_IMPORT.value:
            ud_config.set_value(UpdateDataKeys.Source.LAST_SOURCE_OPTION, option)

    def _handle_save_option_change(self, option: str):
        """Handle save location option change."""
        # Disable save select button when using same as source
        is_same_as_source = option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        if is_same_as_source:
            # Update the save location label to indicate "Same as source..."
            self.view.set_save_path("Same as source...")

            if self.selected_source:
                # If we have a selected source, use its directory
                if self.selected_source["type"] == "folder":
                    self.save_location = self.selected_source["path"]
                else:  # files
                    self.save_location = os.path.dirname(
                        self.selected_source["file_paths"][0]
                    )
                self.info_bar_service.publish_message(
                    f"Save location: {self.save_location}"
                )

                # Notify SimpleStateCoordinator about destination selection
                if self.state_coordinator:
                    self.state_coordinator.set_destination_same_as_source()
        else:
            # Reset save location if not using same as source
            self.save_location = None
            self.view.set_save_path("")  # Clear the save path label
            self.info_bar_service.publish_message("")

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change - drives UI morphing."""
        # Store the state in configuration
        ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

        # Configure UI based on mode using view manager, but preserve checkbox state
        self.view_manager.configure_view_for_workflow_preserve_checkbox(
            self.view,
            is_database_mode=checked,
            preserve_checkbox_state=True
        )

        # Update button text based on mode
        if checked:
            self.view.left_buttons.process_btn.setText("Update Database")
            status_msg = "Database mode: Files will be imported to database"
        else:
            self.view.left_buttons.process_btn.setText("Process Files")
            status_msg = "File utility mode: Files will be processed without database updates"

        self.info_bar_service.publish_message(status_msg)

    def _handle_process(self):
        """Handle process button click."""
        if not self.selected_source:
            self.view.show_error("No source files selected.")
            return

        if not self.save_location:
            self.view.show_error("No save location selected.")
            return

        # Notify SimpleStateCoordinator that processing is starting
        if self.state_coordinator:
            self.state_coordinator.start_processing()

        # Create job sheet
        self.job_sheet_dict = {
            "filepaths": self.selected_source["file_paths"],
            "save_folder": self.save_location,
            "update_database": self.view.get_update_database(),
        }

        # Log the job sheet for debugging
        log.debug(f"Processing job sheet: {self.job_sheet_dict}")

        try:
            # Use the director function to process the files
            result = dw_director(self.job_sheet_dict)

            # Check the result from the director
            if result.get("status") == "success":
                # Notify SimpleStateCoordinator about successful completion
                if self.state_coordinator:
                    processed_count = len(self.job_sheet_dict.get("filepaths", []))
                    self.state_coordinator.complete_processing(processed_count)

                self.view.show_success(result.get("message", "Processing complete!"))
            else:
                error_msg = result.get("message", "An unknown error occurred.")
                log(f"Processing error from director: {result}", "e")
                self.view.show_error(error_msg)

        except Exception as e:
            log.error(f"Error during processing: {e}", "e")
            self.view.show_error(f"An error occurred: {e}")

        # Reset UI
        self.selected_source = None
        self.view.display_selected_source(None)
        self.view.set_exit_mode()

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        global_event_bus.publish(Events.REQUEST_VIEW_TRANSITION.name, target_view)

    def cleanup(self):
        """Clean up before being replaced."""
        # Unsubscribe from events
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self._on_processing_started
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self._on_processing_stats
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()


    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass

    def _on_processing_started(self, job_sheet):
        """Handle processing started event."""
        file_count = len(job_sheet.get("filepaths", [])) or 0
        log.info(f"Processing started for {file_count} files")

        # Update both the InfoBar and the info widget for consistency
        self.info_bar_service.show()
        self.info_bar_service.publish_message(f"Processing {file_count} files...")

    def _on_processing_stats(self, stats):
        """Handle processing stats event."""
        log.debug(f"Processing stats: {stats}")
        total = stats.get("total_files", 0)
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Update progress in the info widget
        self.info_bar_service.publish_message(f"Processing: {processed}/{total} files")

        # Update the InfoBar with current progress
        if total > 0:
            self.info_bar_service.publish_message(
                f"Processing files: {processed}/{total} complete"
            )

    def _on_unrecognized_files(self, unrecognized_files):
        """Handle unrecognized files event."""
        log.warning(f"Unrecognized files detected: {unrecognized_files}")

        # Update the InfoBar with a warning about unrecognized files
        count = len(unrecognized_files)
        self.info_bar_service.publish_message(
            f"Warning: {count} unrecognized file(s) detected"
        )

        # Also update the info widget with detailed error messages
        for file_info in unrecognized_files:
            self.info_bar_service.publish_message(
                f"Warning: Unrecognized file: {file_info['filepath']}\n"
                f"Reason: {file_info.get('reason', 'Unknown')}"
            )

    def _on_processing_completed(self, result):
        """Handle processing completed event."""
        log.info("File processing completed")
        stats = result.get("stats", {})
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Get backup stats if available
        backup_stats = result.get("backup_stats", {})
        backed_up = backup_stats.get("backed_up_count", 0)
        skipped = backup_stats.get("skipped_count", 0)

        # Update the InfoBar with completion message
        status_msg = f"Processing complete. {processed} files processed successfully."
        self.info_bar_service.publish_message(status_msg)

        # Build status message with all stats
        status_msg = f"Processing complete. {processed} files processed successfully."

        # Add backup stats if available
        if backed_up > 0 or skipped > 0:
            status_msg += (
                f" {backed_up} files backed up, {skipped} identical files skipped."
            )

        # Add error info if unrecognized files exist
        if unrecognized > 0:
            status_msg += f" {unrecognized} files unrecognized."
            self.info_bar_service.publish_message(f"Error: {status_msg}")
        else:
            self.info_bar_service.publish_message(status_msg)
