component_id,widget_type,state_initial,text_initial,trigger,response,text_folder_selected,notes
# select_files_group - create base class
select_files_label,Label,,Source Files...,,,"Source folder:",
select_files_optmenu,OptMenu,displayed,Select Folder,On folder selected,folder base_name added Opt Menu displayed,folder name,either option sets the folder
select_files_optmenu_option2,,option 2,Select Files,On files selected,folder base_name added to Opt Menu,Select Files,
select_files_optmenu_option3,,,,,add folder,allows to add another folder to the import folders list
select_files_btn,But<PERSON>,active,[SELECT],,,[SELECT],could change to "options..." if folder set ? - or small cog could appear next to label
# select_save_group
select_save_label,Label,,Archive,On source files folder set,,Archive,
select_save_optmenu,OptMenu,displayed,Same as source,,,Same as source,
select_save_optmenu_option2,,option 2,Select folder...,,"folder base_name added Opt Menu, displayed",Select folder...,Process files disabled until option changed or folder set
select_save_btn,But<PERSON>,disabled,[SELECT],,,[SELECT],
# Process section
update_db_checkbox,Checkbox,Selected,Update Database,,,Update Database,
create_master_checkbox,Checkbox,hidden,Create Master,,,Create Master,possibly redundant as can export any set of transactions from database? An independent csv merge could be offered in an app menu nice to have not required for mvp
process_btn,ActionButton,disabled,PROCESS FILES,,active,PROCESS FILES,
# States
STATE_INITIAL,,,INITIAL,,,,
STATE_FOLDER_SELECTED,,,Folder_selected,,,,
# General Notes
GENERAL_FLOW,,,,,,,the general flow of the app is : configure in left panel view info and edit data in center panel switch task in the right nav side bar.
